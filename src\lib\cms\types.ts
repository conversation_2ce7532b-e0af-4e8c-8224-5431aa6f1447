// CMS Data Types for Frontend Integration

export interface CMSProject {
  id: string
  slug?: string
  title: string
  description: string
  summary: string
  image: string // Base64 encoded image data
  imageMetadata?: {
    filename?: string
    alt?: string
    width?: number
    height?: number
  }
  gallery?: Array<{
    image: string // Base64 encoded image data
    imageMetadata?: {
      filename?: string
      alt?: string
      width?: number
      height?: number
    }
    caption?: string
  }>
  category: string
  pillar: string
  status: string
  location: {
    counties?: CMSCounty[]
    specificLocation?: string
    coordinates?: {
      latitude?: number
      longitude?: number
    }
  }
  timeline: {
    startDate: string
    endDate?: string
    duration?: string
    milestones?: Array<{
      title: string
      description?: string
      targetDate?: string
      completed: boolean
    }>
  }
  budget?: {
    totalBudget?: number
    currency: string
    fundingSources?: Array<{
      source: string
      amount?: number
      percentage?: number
    }>
  }
  impact?: {
    beneficiaries?: number
    communities?: number
    jobsCreated?: number
    metrics?: Array<{
      metric: string
      value: string
      unit?: string
    }>
  }
  featured: boolean
  published: boolean
  tags?: string[]
  slug: string
  createdAt: string
  updatedAt: string
}

export interface CMSSuccessStory {
  id: string
  title: string
  summary: string
  content: string
  image?: CMSMedia
  gallery?: CMSMedia[]
  category: string
  location: {
    county?: CMSCounty
    specificLocation?: string
    coordinates?: {
      latitude?: number
      longitude?: number
    }
  }
  participants: {
    beneficiary?: {
      name: string
      role: string
      organization?: string
      photo?: CMSMedia
    }
    knowledgeHolder?: {
      name: string
      title: string
      expertise?: string
      photo?: CMSMedia
    }
    supporters?: Array<{
      name: string
      role: string
      organization?: string
    }>
  }
  impact?: {
    metrics?: Array<{
      metric: string
      value: string
      unit?: string
      description?: string
    }>
    beneficiaries?: number
    jobsCreated?: number
    incomeIncrease?: {
      percentage?: number
      amount?: number
      currency: string
    }
  }
  testimonials?: Array<{
    quote: string
    author: string
    role: string
    organization?: string
    photo?: CMSMedia
  }>
  timeline: {
    startDate: string
    completionDate?: string
    duration?: string
  }
  investment: {
    totalAmount: number
    currency: string
    sources?: Array<{
      source: string
      amount?: number
      type?: string
    }>
  }
  relatedProject?: any
  featured: boolean
  published: boolean
  tags?: string[]
  slug: string
  createdAt: string
  updatedAt: string
}

export interface CMSResource {
  id: string
  title: string
  description: string
  summary: string
  type: string
  category: string
  file?: CMSMedia
  coverImage?: CMSMedia
  additionalFiles?: Array<{
    title: string
    file: CMSMedia
    description?: string
  }>
  metadata: {
    authors?: Array<{
      name: string
      organization?: string
      role?: string
    }>
    publishDate: string
    lastUpdated?: string
    version: string
    language: string
    pageCount?: number
    fileSize?: string
    isbn?: string
    doi?: string
  }
  access: {
    level: string
    requiresRegistration: boolean
    downloadLimit?: number
  }
  analytics: {
    downloadCount: number
    viewCount: number
    lastDownloaded?: string
  }
  relatedResources?: any[]
  relatedProjects?: any[]
  keywords?: string[]
  featured: boolean
  published: boolean
  externalUrl?: string
  slug: string
  createdAt: string
  updatedAt: string
}

export interface CMSNews {
  id: string
  title: string
  subtitle?: string
  summary: string
  content: string
  featuredImage?: CMSMedia
  gallery?: Array<{
    image: CMSMedia
    caption?: string
    credit?: string
  }>
  category: string
  status: string
  publishDate: string
  author: {
    name: string
    role?: string
    organization?: string
    bio?: string
    photo?: CMSMedia
    email?: string
    socialLinks?: Array<{
      platform: string
      url: string
    }>
  }
  location?: {
    county?: CMSCounty
    specificLocation?: string
    coordinates?: {
      latitude?: number
      longitude?: number
    }
  }
  relatedContent?: {
    projects?: any[]
    successStories?: any[]
    events?: any[]
    resources?: any[]
  }
  seo?: {
    metaTitle?: string
    metaDescription?: string
    keywords?: string[]
    ogImage?: CMSMedia
  }
  engagement: {
    allowComments: boolean
    socialSharing: boolean
    newsletter: boolean
  }
  analytics: {
    viewCount: number
    shareCount: number
    lastViewed?: string
  }
  featured: boolean
  urgent: boolean
  tags?: string[]
  slug: string
  createdAt: string
  updatedAt: string
}

export interface CMSMediaItem {
  id: string
  title: string
  description?: string
  caption?: string
  type: string
  media?: CMSMedia
  thumbnail?: CMSMedia
  category: string
  tags?: string[]
  location?: {
    county?: CMSCounty
    specificLocation?: string
    coordinates?: {
      latitude?: number
      longitude?: number
    }
  }
  dateCreated: string
  event?: any
  project?: any
  credits?: {
    photographer?: string
    videographer?: string
    organization?: string
    copyright?: string
    license: string
  }
  technical?: {
    dimensions?: {
      width?: number
      height?: number
    }
    duration?: string
    fileSize?: string
    format?: string
    quality?: string
  }
  accessibility?: {
    altText?: string
    transcript?: string
    subtitles?: CMSMedia
  }
  usage: {
    allowDownload: boolean
    allowEmbedding: boolean
    commercialUse: boolean
    attribution?: string
  }
  analytics: {
    viewCount: number
    downloadCount: number
    shareCount: number
    lastViewed?: string
  }
  featured: boolean
  published: boolean
  slug: string
  createdAt: string
  updatedAt: string
}

export interface CMSInvestmentOpportunity {
  id: string
  title: string
  description: string
  summary: string
  sector: string
  investmentType: string
  status: string
  image?: CMSMedia
  gallery?: CMSMedia[]
  financial: {
    fundingRequired: number
    currency: string
    fundingStages?: Array<{
      stage: string
      amount: number
      timeline?: string
      milestones?: string
    }>
    useOfFunds?: Array<{
      category: string
      amount: number
      percentage?: number
      description?: string
    }>
    expectedReturns?: {
      roi?: number
      paybackPeriod?: string
      revenueProjections?: Array<{
        year: number
        revenue: number
        profit?: number
      }>
    }
  }
  businessModel?: {
    valueProposition: string
    targetMarket: string
    competitiveAdvantage?: string
    revenueStreams?: Array<{
      stream: string
      description?: string
      projectedRevenue?: number
    }>
    keyPartners?: Array<{
      partner: string
      role: string
      contribution?: string
    }>
  }
  location: {
    counties: CMSCounty[]
    specificLocation?: string
    coordinates?: {
      latitude?: number
      longitude?: number
    }
  }
  timeline?: {
    applicationDeadline?: string
    expectedStartDate?: string
    projectDuration?: string
    milestones?: Array<{
      milestone: string
      targetDate?: string
      description?: string
    }>
  }
  requirements?: {
    investorCriteria?: Array<{
      criterion: string
      description?: string
      mandatory: boolean
    }>
    minimumInvestment?: number
    maximumInvestment?: number
    investorType?: string[]
    documentation?: Array<{
      document: string
      required: boolean
      description?: string
    }>
  }
  impact?: {
    socialImpact?: string
    environmentalImpact?: string
    economicImpact?: string
    beneficiaries?: number
    jobsCreated?: number
    sdgAlignment?: Array<{
      sdg: string
      description?: string
    }>
  }
  team?: {
    projectLead?: {
      name: string
      role: string
      bio?: string
      photo?: CMSMedia
      email?: string
    }
    keyPersonnel?: Array<{
      name: string
      role: string
      expertise?: string
      bio?: string
    }>
  }
  documents?: Array<{
    title: string
    file: CMSMedia
    type?: string
    confidential: boolean
  }>
  applicationProcess?: {
    steps?: Array<{
      step: string
      description?: string
      duration?: string
    }>
    contactPerson?: {
      name: string
      role: string
      email: string
      phone?: string
    }
    applicationForm?: CMSMedia
  }
  featured: boolean
  urgent: boolean
  tags?: string[]
  slug: string
  createdAt: string
  updatedAt: string
}

export interface CMSMedia {
  id: string
  filename: string
  url: string
  alt?: string
  width?: number
  height?: number
  mimeType?: string
  filesize?: number
}

export interface CMSCounty {
  id: string
  name: string
  code?: string
}

// API Response Types
export interface CMSResponse<T> {
  data: T
  totalDocs?: number
  page?: number
  limit?: number
  totalPages?: number
  hasNextPage?: boolean
  hasPrevPage?: boolean
}

export interface CMSListResponse<T> {
  data: T[]
  totalDocs: number
  page: number
  limit: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

// Query Parameters
export interface CMSQueryParams {
  page?: number
  limit?: number
  sort?: string
  search?: string
  category?: string
  featured?: boolean
  [key: string]: any
}
